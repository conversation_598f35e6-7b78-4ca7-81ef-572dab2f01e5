{"name": "@skywind-group/sw-game-core", "version": "0.9.22", "description": "", "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "compile": "tsc || true", "gulp": "gulp", "lint": "eslint 'src/**/*.ts' --ignore-pattern 'src/test/**/*.ts'", "lint:fix": "eslint 'src/**/*.ts' --ignore-pattern 'src/test/**/*.ts' --fix", "pretest": "npm run clean && npm run compile", "test": "npm run test:unit", "test:mocha-with-coverage": "istanbul cover _mocha -- lib/test/**/*.spec.js --reporter mocha-jen<PERSON>-reporter --reporter-options junit_report_name=Tests,junit_report_path=coverage/xunit.xml,junit_report_stack=1 && cp coverage/coverage.json coverage/coverage-final.json", "test:remap": "remap-istanbul --input coverage/coverage-final.json --output coverage/remap/lcov.info --type lcovonly && remap-istanbul --input coverage/coverage-final.json --output coverage/remap/coverage.json --type json && remap-istanbul --input coverage/coverage-final.json --output coverage/remap/html-report --type html && remap-istanbul --input coverage/coverage-final.json --output coverage/remap/cobertura.xml --type cobertura", "test:unit": "npm run test:mocha-with-coverage && npm run test:remap"}, "devDependencies": {"@skywind-group/sw-random-cs": "1.0.8", "@types/bluebird": "3.5.2", "@types/bluebird-global": "3.5.2", "@types/chai": "4.0.1", "@types/chai-as-promised": "0.0.31", "@types/lodash": "4.14.66", "@types/mocha": "2.2.41", "@types/node": "8.0.13", "@types/sinon": "2.2.1", "@types/sinon-chai": "2.7.28", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "chai": "4.0.2", "chai-as-promised": "7.0.0", "del": "2.2.2", "eslint": "^8.57.1", "gulp": "3.9.1", "gulp-istanbul": "1.1.2", "gulp-mocha": "3.0.1", "gulp-typescript": "3.1.7", "istanbul": "0.4.5", "merge2": "1.0.2", "mocha-jenkins-reporter": "0.3.8", "remap-istanbul": "0.9.5", "rimraf": "^3.0.2", "run-sequence": "1.2.2", "sinon": "3.1.0", "typescript": "^4.9.5"}, "dependencies": {"lodash": "4.17.4", "uuid": "3.1.0"}}